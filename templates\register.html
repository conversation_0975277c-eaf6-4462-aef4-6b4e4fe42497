<!DOCTYPE html>
<html>
<head>
    <title>Register User - File Processing Pipeline</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .custom-step-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .custom-step-row input {
            flex: 1;
            margin-right: 10px;
        }
        .add-custom-step-btn, .remove-custom-step-btn {
            width: 30px;
            height: 30px;
            padding: 0;
            line-height: 1;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Register New User</h1>

        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="flash-messages">
            {% for message in messages %}
            <div class="flash-message">{{ message }}</div>
            {% endfor %}
        </div>
        {% endif %}
        {% endwith %}

        <form action="{{ url_for('register') }}" method="post">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-group">
                <label>Assigned Roles:</label>
                <p class="help-text">These roles will be automatically assigned to the user for all new files.</p>
                <div class="checkbox-group">
                    {% for step in steps %}
                    <div class="checkbox-item">
                        <input type="checkbox" id="step_{{ step }}" name="assigned_roles" value="{{ step }}">
                        <label for="step_{{ step }}">{{ step|capitalize }}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="form-group">
                <label>Custom Steps:</label>
                <p class="help-text">Add custom steps that are not in the original steps list. These will be stored with the user's profile and the user will be automatically assigned to these steps if they are ever added to a flow.</p>
                <div id="custom-steps-container">
                    <div class="custom-step-row">
                        <input type="text" name="custom_steps" placeholder="Enter custom step name">
                        <button type="button" class="btn btn-small add-custom-step-btn">+</button>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="is_admin" name="is_admin">
                    <label for="is_admin">Administrator</label>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn">Register User</button>
                <a href="{{ url_for('index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>

    <script>
        // Handle adding more custom step fields
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('add-custom-step-btn')) {
                const container = document.getElementById('custom-steps-container');
                const newRow = document.createElement('div');
                newRow.className = 'custom-step-row';
                newRow.innerHTML = `
                    <input type="text" name="custom_steps" placeholder="Enter custom step name">
                    <button type="button" class="btn btn-small remove-custom-step-btn">-</button>
                `;
                container.appendChild(newRow);
            } else if (event.target.classList.contains('remove-custom-step-btn')) {
                event.target.closest('.custom-step-row').remove();
            }
        });
    </script>
</body>
</html>
