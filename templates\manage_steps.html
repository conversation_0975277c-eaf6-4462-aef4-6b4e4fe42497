<!DOCTYPE html>
<html>
<head>
    <title>Manage Process Steps</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <style>
        .custom-step-badge {
            display: inline-block;
            background-color: #3498db;
            color: white;
            font-size: 0.8em;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
            vertical-align: middle;
        }

        .time-input-container {
            display: flex;
            align-items: center;
        }

        .assigned-time-input {
            width: 100px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>File Processing Pipeline</h1>
        <div class="user-info">
            <span>Welcome, {{ username }}</span>
            <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            <a href="{{ url_for('index') }}" class="btn btn-small">Back to Overview</a>
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
    <div class="flash-messages">
        {% for message in messages %}
        <div class="flash-message">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <div class="container">
        <div class="actions-bar">
            <h2>Manage Process Steps</h2>
            <button id="add-step-btn" class="btn">Add New Step</button>
        </div>

        <div class="warning-box">
            <p><strong>Warning:</strong> Changing the process steps will affect all files in the system.
            Removing a step will delete all associated data for that step.</p>
        </div>

        <div class="table-container">
            <table class="steps-table">
                <thead>
                    <tr>
                        <th>Order</th>
                        <th>Step Name</th>
                        <th>Files in Step</th>
                        <th>Default Assigned Time (DD:HH:MM)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="steps-tbody">
                    {% for step in steps %}
                    <tr data-step="{{ step }}">
                        <td>{{ loop.index }}</td>
                        <td>
                            <span class="step-name">{{ step|capitalize }}</span>
                            {% if step in custom_steps %}
                            <span class="custom-step-badge">Custom</span>
                            {% endif %}
                            <input type="text" class="step-edit-input hidden" value="{{ step }}">
                        </td>
                        <td>{{ step_counts[step] }}</td>
                        <td>
                            <div class="time-input-container">
                                <input type="text" class="assigned-time-input" data-step="{{ step }}"
                                    value="{{ '%02d:%02d:%02d'|format(default_assigned_times[step] // 1440, (default_assigned_times[step] % 1440) // 60, default_assigned_times[step] % 60) if step in default_assigned_times else '00:00:00' }}"
                                    placeholder="DD:HH:MM" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}">
                            </div>
                        </td>
                        <td>
                            <button class="btn btn-small edit-step-btn">Edit</button>
                            <button class="btn btn-small save-step-btn hidden">Save</button>
                            <button class="btn btn-small cancel-step-btn hidden">Cancel</button>
                            <button class="btn btn-small btn-danger remove-step-btn" {% if step_counts[step] > 0 %}disabled{% endif %}>Remove</button>
                            {% if not loop.first %}
                            <button class="btn btn-small move-up-btn">↑</button>
                            {% endif %}
                            {% if not loop.last %}
                            <button class="btn btn-small move-down-btn">↓</button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Add Step Modal -->
        <div id="add-step-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add New Process Step</h2>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="add-step-form" action="{{ url_for('add_step') }}" method="post">
                        <div class="form-group">
                            <label for="step_name">Step Name:</label>
                            <input type="text" id="step_name" name="step_name" required>
                        </div>
                        <div class="form-group">
                            <label for="step_position">Position:</label>
                            <select id="step_position" name="step_position">
                                <option value="start">At the beginning</option>
                                <option value="end" selected>At the end</option>
                                {% for step in steps %}
                                <option value="after_{{ step }}">After {{ step|capitalize }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="default_assigned_time">Default Assigned Time (DD:HH:MM):</label>
                            <input type="text" id="default_assigned_time" name="default_assigned_time" value="00:00:00" pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}" placeholder="DD:HH:MM">
                            <small>Format: Days:Hours:Minutes (e.g., 00:05:30 for 5 hours and 30 minutes)</small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn">Add Step</button>
                            <button type="button" class="btn btn-secondary close-modal-btn">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Function to convert DD:HH:MM format to minutes
        function timeToMinutes(timeStr) {
            // Check if the format is correct
            const regex = /^(\d{2}):(\d{2}):(\d{2})$/;
            const match = timeStr.match(regex);

            if (!match) {
                return null; // Invalid format
            }

            const days = parseInt(match[1], 10);
            const hours = parseInt(match[2], 10);
            const minutes = parseInt(match[3], 10);

            // Validate ranges
            if (hours >= 24 || minutes >= 60) {
                return null; // Invalid time values
            }

            // Convert to total minutes
            return days * 24 * 60 + hours * 60 + minutes;
        }

        // Function to convert minutes to DD:HH:MM format
        function minutesToTime(totalMinutes) {
            if (isNaN(totalMinutes) || totalMinutes < 0) {
                return '00:00:00';
            }

            const days = Math.floor(totalMinutes / (24 * 60));
            const hours = Math.floor((totalMinutes % (24 * 60)) / 60);
            const minutes = totalMinutes % 60;

            return `${days.toString().padStart(2, '0')}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        // Add step modal
        const addStepModal = document.getElementById('add-step-modal');
        const addStepBtn = document.getElementById('add-step-btn');

        addStepBtn.addEventListener('click', function() {
            addStepModal.classList.remove('hidden');
        });

        document.querySelectorAll('.close-modal, .close-modal-btn').forEach(element => {
            element.addEventListener('click', function() {
                addStepModal.classList.add('hidden');
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === addStepModal) {
                addStepModal.classList.add('hidden');
            }
        });

        // Edit step name
        document.querySelectorAll('.edit-step-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                row.querySelector('.step-name').classList.add('hidden');
                row.querySelector('.step-edit-input').classList.remove('hidden');
                row.querySelector('.edit-step-btn').classList.add('hidden');
                row.querySelector('.save-step-btn').classList.remove('hidden');
                row.querySelector('.cancel-step-btn').classList.remove('hidden');
            });
        });

        // Cancel edit
        document.querySelectorAll('.cancel-step-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                row.querySelector('.step-name').classList.remove('hidden');
                row.querySelector('.step-edit-input').classList.add('hidden');
                row.querySelector('.edit-step-btn').classList.remove('hidden');
                row.querySelector('.save-step-btn').classList.add('hidden');
                row.querySelector('.cancel-step-btn').classList.add('hidden');

                // Reset input value to original
                const originalName = row.querySelector('.step-name').textContent;
                row.querySelector('.step-edit-input').value = originalName;
            });
        });

        // Save step name
        document.querySelectorAll('.save-step-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                const oldStep = row.getAttribute('data-step');
                const newStep = row.querySelector('.step-edit-input').value.trim();

                if (newStep === '') {
                    alert('Step name cannot be empty');
                    return;
                }

                // Send update to server
                fetch('/rename_step', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        old_step: oldStep,
                        new_step: newStep
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI
                        row.setAttribute('data-step', newStep);
                        row.querySelector('.step-name').textContent = newStep.charAt(0).toUpperCase() + newStep.slice(1);
                        row.querySelector('.step-name').classList.remove('hidden');
                        row.querySelector('.step-edit-input').classList.add('hidden');
                        row.querySelector('.edit-step-btn').classList.remove('hidden');
                        row.querySelector('.save-step-btn').classList.add('hidden');
                        row.querySelector('.cancel-step-btn').classList.add('hidden');

                        // Show success message
                        alert('Step renamed successfully');
                    } else {
                        alert('Failed to rename step: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while renaming the step');
                });
            });
        });

        // Remove step
        document.querySelectorAll('.remove-step-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (this.disabled) {
                    alert('Cannot remove a step that has files. Move files to another step first.');
                    return;
                }

                const row = this.closest('tr');
                const step = row.getAttribute('data-step');

                if (confirm(`Are you sure you want to remove the step "${step}"? This action cannot be undone.`)) {
                    // Send delete request to server
                    fetch('/remove_step', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            step: step
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove row from table
                            row.remove();
                            alert('Step removed successfully');

                            // Reload page to update order numbers
                            window.location.reload();
                        } else {
                            alert('Failed to remove step: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while removing the step');
                    });
                }
            });
        });

        // Move step up
        document.querySelectorAll('.move-up-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                const step = row.getAttribute('data-step');

                fetch('/move_step', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        step: step,
                        direction: 'up'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Failed to move step: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while moving the step');
                });
            });
        });

        // Move step down
        document.querySelectorAll('.move-down-btn').forEach(button => {
            button.addEventListener('click', function() {
                const row = this.closest('tr');
                const step = row.getAttribute('data-step');

                fetch('/move_step', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        step: step,
                        direction: 'down'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('Failed to move step: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while moving the step');
                });
            });
        });

        // Handle default assigned time updates
        document.querySelectorAll('.assigned-time-input').forEach(input => {
            input.addEventListener('change', function() {
                const step = this.getAttribute('data-step');
                const timeValue = this.value.trim();

                // Validate format
                if (!timeValue.match(/^\d{2}:\d{2}:\d{2}$/)) {
                    alert('Please enter time in DD:HH:MM format (e.g., 00:05:30 for 5 hours and 30 minutes)');
                    this.value = '00:00:00';
                    return;
                }

                // Convert to minutes
                const assignedTimeMinutes = timeToMinutes(timeValue);

                if (assignedTimeMinutes === null) {
                    alert('Invalid time format. Please use DD:HH:MM where HH < 24 and MM < 60');
                    this.value = '00:00:00';
                    return;
                }

                // Send update to server
                fetch('/update_default_assigned_time', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        step: step,
                        assigned_time: assignedTimeMinutes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert('Default assigned time updated successfully');
                    } else {
                        alert('Failed to update default assigned time: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while updating the default assigned time');
                });
            });
        });
    </script>
</body>
</html>
