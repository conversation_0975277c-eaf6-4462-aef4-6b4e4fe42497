/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
    padding-bottom: 20px;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3, h4 {
    margin-bottom: 15px;
    color: #333;
}

/* Header */
.header {
    background-color: #2c3e50;
    color: white;
    padding: 15px 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Buttons */
.btn {
    display: inline-block;
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    text-decoration: none;
    font-size: 15px;
    transition: background 0.3s;
}

.btn:hover {
    background: #2980b9;
}

.btn-small {
    padding: 5px 10px;
    font-size: 14px;
}

.btn-secondary {
    background: #95a5a6;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.help-text {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 10px;
    font-style: italic;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}

/* Status Colors */
.status-not-started {
    color: #e74c3c;
}

.status-in-progress {
    color: #f39c12;
}

.status-completed {
    color: #27ae60;
}

.disabled-text {
    color: #95a5a6;
    font-style: italic;
}

/* Actions Bar */
.actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

/* File Info */
.file-info {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-info p {
    margin-bottom: 10px;
}

.file-info p:last-child {
    margin-bottom: 0;
}

.current-step-indicator {
    font-weight: bold;
    color: #3498db;
    background-color: #eaf2fa;
    padding: 3px 8px;
    border-radius: 3px;
    border: 1px solid #bde0ff;
}

/* Progress Bar */
.progress-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    flex-wrap: wrap;
    gap: 5px;
}

.progress-step {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    position: relative;
    cursor: pointer;
}

.progress-step.completed {
    background-color: #2ecc71;
    border-color: #27ae60;
}

.progress-step.current {
    background-color: #3498db;
    border-color: #2980b9;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
}

.progress-text {
    margin-left: 10px;
    font-size: 14px;
    color: #7f8c8d;
}

/* Pipeline Visualization */
.pipeline-wrapper {
    position: relative;
    margin: 30px 0;
    overflow: hidden;
}

.pipeline-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 30px;
    padding: 20px;
    position: relative;
    min-height: 300px;
}

.pipeline-step {
    position: relative;
    z-index: 2;
    margin-bottom: 20px;
}

.pipeline-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    width: 280px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #ddd;
    transition: transform 0.3s, box-shadow 0.3s;
}

.pipeline-step.current .pipeline-card {
    border-color: #3498db;
    box-shadow: 0 6px 12px rgba(52, 152, 219, 0.3);
    transform: translateY(-5px);
}

.pipeline-card h3 {
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    font-size: 18px;
    color: #2c3e50;
}

.status-indicator {
    text-align: center;
    font-weight: bold;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.card-details {
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.card-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.status-select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ddd;
    flex-grow: 1;
    font-size: 14px;
    min-width: 120px;
}

.btn-upload {
    background-color: #27ae60;
}

.btn-upload:hover {
    background-color: #219955;
}

.manage-users-btn {
    background-color: #9b59b6;
}

.manage-users-btn:hover {
    background-color: #8e44ad;
}

textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
}

/* SVG Connections */
.pipeline-connections {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.connector-path {
    stroke: #3498db;
    stroke-width: 2;
    fill: none;
    stroke-dasharray: 5, 5;
    animation: dash 30s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: 1000;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 5px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    color: #aaa;
}

.close-modal:hover {
    color: #333;
}

/* Download Items */
.download-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.download-item:last-child {
    border-bottom: none;
}

.download-info {
    flex-grow: 1;
}

.download-info p {
    margin-bottom: 5px;
}

/* Files Table */
.files-table td {
    vertical-align: middle;
}

.no-data {
    text-align: center;
    padding: 20px;
    color: #7f8c8d;
}

/* Flash Messages */
.flash-messages {
    margin: 15px 5%;
}

.flash-message {
    padding: 10px 15px;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    margin-bottom: 10px;
}

/* Login */
.login-container {
    max-width: 400px;
    margin: 50px auto;
    background: white;
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.info {
    margin-top: 20px;
    font-size: 14px;
    color: #7f8c8d;
    text-align: center;
}

/* Step Management */
.steps-table .btn {
    margin-right: 5px;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-danger[disabled] {
    background-color: #f5b7b1;
    cursor: not-allowed;
}

.step-edit-input {
    width: 100%;
    padding: 5px;
    border: 1px solid #3498db;
    border-radius: 3px;
}

.warning-box {
    background-color: #fcf8e3;
    border: 1px solid #faebcc;
    color: #8a6d3b;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Utilities */
.hidden {
    display: none !important;
}

.sort-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1em;
    color: #3498db;
    margin-left: 4px;
    padding: 0 2px;
    vertical-align: middle;
    transition: color 0.2s;
}

.sort-btn:hover {
    color: #2980b9;
}

#filter-supplier, #filter-process-type, #filter-step, #filter-status {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
    font-size: 15px;
    margin-left: 4px;
    margin-right: 10px;
    min-width: 120px;
}
