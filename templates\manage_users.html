<!DOCTYPE html>
<html>
<head>
    <title>Manage Users</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="header">
        <h1>File Processing Pipeline</h1>
        <div class="user-info">
            <span>Welcome, {{ username }}</span>
            <a href="{{ url_for('logout') }}" class="btn btn-small">Logout</a>
            <a href="{{ url_for('index') }}" class="btn btn-small">Back to Overview</a>
        </div>
    </div>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
    <div class="flash-messages">
        {% for message in messages %}
        <div class="flash-message">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <div class="container">
        <div class="actions-bar">
            <h2>Manage Users</h2>
            <a href="{{ url_for('register') }}" class="btn">Add New User</a>
        </div>

        <div class="warning-box">
            <p><strong>Warning:</strong> Deleting a user will remove all their access rights and assignments. This action cannot be undone.</p>
        </div>

        <div class="table-container">
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Username</th>
                        <th>Admin</th>
                        <th>Assigned Roles</th>
                        <th>Custom Steps</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for username, user in users.items() %}
                    <tr>
                        <td>{{ username }}</td>
                        <td>{{ "Yes" if user.is_admin else "No" }}</td>
                        <td>{{ user.roles|join(', ')|capitalize if user.roles else "None" }}</td>
                        <td>{{ user.custom_steps|join(', ')|capitalize if user.custom_steps else "None" }}</td>
                        <td>
                            {% if username != 'admin' %}
                            <button class="btn btn-small btn-danger delete-user-btn" data-username="{{ username }}">Delete</button>
                            {% else %}
                            <span class="disabled-text">Cannot delete admin</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Delete user handling
        document.querySelectorAll('.delete-user-btn').forEach(button => {
            button.addEventListener('click', function() {
                const username = this.getAttribute('data-username');

                if (confirm(`Are you sure you want to delete the user "${username}"? This action cannot be undone.`)) {
                    // Send delete request to server
                    fetch('/delete_user', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            username: username
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Remove row from table
                            this.closest('tr').remove();
                            alert('User deleted successfully');
                        } else {
                            alert('Failed to delete user: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the user');
                    });
                }
            });
        });
    </script>
</body>
</html>
